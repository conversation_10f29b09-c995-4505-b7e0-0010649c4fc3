using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.OrganizationMappings;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Services.Apis.CloudStack;
using MyAdaptiveCloud.Services.Apis.CloudStack.Model;
using MyAdaptiveCloud.Services.Apis.CloudStack.Requests;
using MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.CloudInfra;
using MyAdaptiveCloud.Services.Exceptions;
using Dto = MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using User = MyAdaptiveCloud.Services.Apis.CloudStack.Model.User;

namespace MyAdaptiveCloud.Services.Services
{
    public class CloudInfraService : ICloudInfraService
    {
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly ICloudStackApi _cloudStackApi;
        private readonly IAdaptiveCloudService _adaptiveCloudService;
        private readonly IOrganizationService _organizationService;
        private readonly IConnectWiseService _connectWiseService;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IPersonRepository _personRepository;
        private readonly IPersonService _personService;
        private readonly ILogger<CloudInfraService> _logger;
        private readonly IOrganizationMappingRepository _organizationMappingRepository;
        private readonly IConfigurationService _configurationService;

        public CloudInfraService(
            MyAdaptiveCloudContext context,
            ICloudStackApi cloudStackApi,
            IOrganizationService organizationService,
            IConnectWiseService connectWiseService,
            IAdaptiveCloudService adaptiveCloudService,
            IOrganizationRepository organizationRepository,
            IPersonRepository personRepository,
            IPersonService personService,
            IOrganizationMappingRepository organizationMappingRepository,
            IConfigurationService configurationService,
            ILogger<CloudInfraService> logger)
        {
            _dbContext = context;
            _cloudStackApi = cloudStackApi;
            _organizationService = organizationService;
            _connectWiseService = connectWiseService;
            _adaptiveCloudService = adaptiveCloudService;
            _organizationRepository = organizationRepository;
            _personRepository = personRepository;
            _personService = personService;
            _organizationMappingRepository = organizationMappingRepository;
            _configurationService = configurationService;
            _logger = logger;
        }

        public async Task<bool> GetIsOrganizationMappedToCloudInfra(int organizationId)
        {
            return await _dbContext.CloudInfraOrganizationMapping.AsNoTracking()
                .Include(a => a.Organization)
                .Where(a => organizationId == a.OrganizationId).AnyAsync();
        }

        public async Task<AccountListModel> GetCloudInfraAccount(int organizationId)
        {
            var organization = await _organizationService.GetActiveOrganizationById(organizationId, false, true);

            var organizationMapping = await _adaptiveCloudService.GetMapping(organizationId);

            var account = organizationMapping != null ? await _cloudStackApi.GetAccountById(organizationMapping.AccountId.Value) : null;

            var isTopLevelOrg = organizationId == Constants.RootOrganizationId || (await _organizationRepository.GetAncestorOrganizations(organizationId)).Count() == 1;

            var mappedAccount = new AccountListModel
            {
                Id = organizationId,
                CloudAccountId = account != null ? account.Id : null,
                DomainName = account != null ? account.DomainName : null,
                AccountName = account != null ? account.Name : "",
                OrganizationName = organization.Name ?? "",
                OrganizationId = organization.OrganizationId,
                UsersCount = account != null ? account.user.Count.ToString() : "",
                IsMappedToCloudInfraAccount = !string.IsNullOrEmpty(account?.Name),
                IsPartner = organization.IsPartner,
                IsTopLevelOrganization = isTopLevelOrg
            };

            return mappedAccount;
        }

        public async Task<List<AccountListModel>> GetCloudInfastructureAccounts(int organizationId)
        {
            var organization = await _organizationService.GetActiveOrganizationById(organizationId, false, true);

            var isRootOrg = organization.OrganizationId == Constants.RootOrganizationId;

            var responseList = new List<AccountListModel>();

            var organizationQuery = _organizationRepository.GetDescendantOrganizations(organizationId);

            var organizationIds = new List<int>();

            if (isRootOrg)
            {
                organizationIds = organizationQuery.Where(a => a.OrganizationId != organizationId && a.ParentOrganizationId == organizationId).Select(o => o.OrganizationId)
                    .ToList();
            }
            else
            {
                organizationIds = organizationQuery.Where(a => a.OrganizationId != organizationId).Select(o => o.OrganizationId).ToList();
            }


            var organizationMapping = await _dbContext.CloudInfraOrganizationMapping.AsNoTracking()
                .Include(a => a.Organization)
                .Where(a => organizationIds.Contains(a.OrganizationId)).ToListAsync();

            var currentOrganizationMapping = await _adaptiveCloudService.GetMapping(organizationId);

            var accountTask = new List<AccountResponse>();

            if (isRootOrg)
            {
                accountTask = await _cloudStackApi.GetAllAccounts();
            }
            else if (currentOrganizationMapping.DomainId != null && currentOrganizationMapping.DomainId.Value != Guid.Empty)
            {
                accountTask = await _cloudStackApi.GetAccountsForDomainId(currentOrganizationMapping.DomainId.Value);
            }
            else if (currentOrganizationMapping.AccountId != null && currentOrganizationMapping.AccountId.Value != Guid.Empty)
            {
                accountTask.Add(await _cloudStackApi.GetAccountById(currentOrganizationMapping.AccountId.Value));
            }

            foreach (var orgId in organizationIds)
            {
                var account = new AccountResponse();
                var orgMapping = new CloudInfraOrganizationMapping();
                orgMapping = organizationMapping.FirstOrDefault(a => a.OrganizationId == orgId);
                var currOrganization = await _organizationService.GetActiveOrganizationById(orgId, false, true);

                if (orgMapping != null)
                {
                    account = accountTask.FirstOrDefault(a => a.Id == orgMapping.AccountId);
                    if (account != null && account.Id != Guid.Empty)
                    {
                        var mappedOrg = new AccountListModel
                        {
                            Id = orgMapping.OrganizationId,
                            CloudAccountId = account != null ? account.Id : null,
                            DomainName = account != null ? account.DomainName : Applications.CloudInfra.ToString(),
                            AccountName = account != null ? account.Name : "",
                            OrganizationName = orgMapping.Organization?.Name ?? "",
                            OrganizationId = orgMapping.OrganizationId,
                            UsersCount = account != null ? account.user.Count.ToString() : "0",
                            IsMappedToCloudInfraAccount = !string.IsNullOrEmpty(account?.Name),
                            IsPartner = currOrganization.IsPartner
                        };
                        responseList.Add(mappedOrg);
                    }
                    else
                    {
                        // We need to return an entry back for this org, even though we cannot find the associated Cloud Infrastructure
                        // account. For now, we can treat this like we have found it, as it was/is associated to something.
                        var mappedOrg = new AccountListModel
                        {
                            Id = orgMapping.OrganizationId,
                            CloudAccountId = orgMapping.AccountId,
                            DomainName = orgMapping.DomainName,
                            AccountName = orgMapping.AccountName,
                            OrganizationName = orgMapping.Organization?.Name ?? "",
                            OrganizationId = orgMapping.OrganizationId,
                            UsersCount = "-",
                            IsMappedToCloudInfraAccount = !string.IsNullOrEmpty(account?.Name),
                            IsPartner = currOrganization.IsPartner
                        };
                        responseList.Add(mappedOrg);
                    }
                }
                else
                {
                    //No Related Org

                    var org = new AccountListModel
                    {
                        Id = currOrganization.OrganizationId,
                        CloudAccountId = null,
                        DomainName = "",
                        AccountName = "",
                        OrganizationName = currOrganization.Name,
                        OrganizationId = currOrganization.OrganizationId,
                        UsersCount = "0",
                        IsMappedToCloudInfraAccount = false,
                        IsPartner = currOrganization.IsPartner
                    };

                    responseList.Add(org);
                }
            }

            return responseList.OrderBy(a => a.OrganizationName).ToList();
        }

        public async Task UpdateUserOrganization(Guid cloudInfraUserId, int userId, int organizationId)
        {
            await DeleteUser(cloudInfraUserId.ToString());
            await CreateUser(organizationId, userId);
        }

        public async Task DeleteUser(string userId)
        {
            await _cloudStackApi.DeleteUser(userId);
        }

        public async Task CreateUser(int organizationId, int userId)
        {
            CreateUser userRequest = new CreateUser();
            var organization = await _adaptiveCloudService.GetMapping(organizationId);
            var person = await _personRepository.GetPersonById(userId, true);
            AccountResponse account = new AccountResponse();
            List<User> userAccounts = new List<User>();
            if (organization != null)
            {
                account = await _cloudStackApi.GetAccountById(new Guid(organization.AccountId.ToString()));
                if (account != null)
                {
                    userRequest.Account = account.Name;
                    userRequest.DomainId = account.DomainId;
                }
            }

            if (person != null)
            {
                userAccounts = await _cloudStackApi.GetUsersByUsername(person.Email);
                userRequest.UserName = person.Email;
                userRequest.Email = person.Email;
                userRequest.FirstName = person.FirstName;
                userRequest.LastName = person.LastName;
            }

            try
            {
                var accountsInSameDomainAsSelectedOrg = userAccounts.Where(a => a.DomainId == account.DomainId && a.AccountId != account.Id);
                _logger.LogInformation("Cloud Infra Service - Create User: User found in: " + userAccounts.Count + " accounts.");

                foreach (var a in userAccounts)
                {
                    _logger.LogInformation("Cloud Infra Service - Create User: User found in: " + a.Account + " account.");
                }


                if (accountsInSameDomainAsSelectedOrg.Any())
                {
                    _logger.LogInformation("Cloud Infra Service - Create User ERROR: User already exist in another account in the same Domain");
                    //User already exist in another account in the same Domain
                    throw new BadRequestException("Duplicated entry. User already added to an existing account.");
                }

                _logger.LogInformation("Cloud Infra Service - Create User");
                var ciUserResponse = await _cloudStackApi.CreateUser(userRequest);
                if (ciUserResponse.Id != Guid.Empty)
                {
                    var idp = await _cloudStackApi.GetIdp();
                    await _cloudStackApi.AuthorizeSamlSso(ciUserResponse.Id, authorized: true, idp);
                }
            }
            catch
            {
                throw new BadRequestException("Duplicated entry. User already added to an existing account.");
            }
        }

        public async Task<List<CloudInfraUserListModel>> GetUsers(int organizationId, bool showSubOrgUsers)
        {
            var userList = new List<CloudInfraUserListModel>();

            List<Person> orgUsers;
            if (showSubOrgUsers)
            {
                orgUsers = _dbContext.OrgHierarchyUsers(organizationId, false).ToList();
            }
            else
            {
                orgUsers = (await _personService.GetPersonsWithUserRolesInOrganizationByOrganizationId(organizationId)).Where(a => !a.IsApiUser).ToList();
            }

            var organization = await _organizationService.GetActiveOrganizationById(organizationId, false, true);
            var organizationMapping = await _adaptiveCloudService.GetMapping(organizationId);
            var parentOrganization = await _organizationService.GetRootOrganization(organizationId, true);
            var parentOrganizationMapping = await _adaptiveCloudService.GetMapping(parentOrganization.OrganizationId);
            var organizationAccount = await GetOrganizationAccount(organizationMapping);

            var isRootOrg = parentOrganizationMapping.OrganizationId == Constants.RootOrganizationId;
            var isPartner = organization.IsPartner;

            var accountAllUsers = new List<User>();

            if (parentOrganizationMapping.DomainId != null)
            {
                accountAllUsers.AddRange(await _cloudStackApi.GetUsersByDomain(parentOrganizationMapping.DomainId.Value.ToString()));
            }

            if (!isRootOrg)
            {
                var rootOrgMapping = await _adaptiveCloudService.GetMapping(Constants.RootOrganizationId);
                if (rootOrgMapping.DomainId == organizationMapping.DomainId)
                {
                    var rootAccount = await _cloudStackApi.GetAccountById(rootOrgMapping.AccountId.Value);
                    var rootUsersByAccount = rootAccount?.user ?? new List<Apis.CloudStack.Model.User>();
                    accountAllUsers.AddRange(rootUsersByAccount);
                }


                if (organizationMapping.AccountId != null)
                {
                    var usersByAccount = organizationAccount?.user ?? new List<Apis.CloudStack.Model.User>();
                    accountAllUsers.AddRange(usersByAccount);
                }
            }

            var accountUsers = isPartner
                ? accountAllUsers.Where(a => a.RoleName != "Root Admin")
                : accountAllUsers;

            //Show Only show users that belongs to a DB stored organization: Requested in https://ippathways.atlassian.net/browse/MYAC-1220?focusedCommentId=13935
            var accountUsersIds = accountUsers.Select(a => a.AccountId);

            var accountUsersMappings = await _adaptiveCloudService.GetMappingByAccountIds(accountUsersIds);

            var accountUsersMappingsIds = accountUsersMappings.Select(a => a.AccountId.ToString());

            var accountUsersMapped = accountUsers.Where(a => accountUsersMappingsIds.Contains(a.AccountId.ToString()));

            if (parentOrganizationMapping != null)
            {
                var mappedUsers = (from orgUser in orgUsers
                    join cs in accountUsersMapped on orgUser.Email equals cs.Email into temp
                    from a in temp.DefaultIfEmpty()
                    select new CloudInfraUserListModel
                    {
                        UserId = orgUser.PersonId,
                        CloudInfraUserId = a?.Id,
                        FirstName = orgUser.FirstName,
                        LastName = orgUser.LastName,
                        Email = orgUser.Email,
                        AccountName = a?.Account ?? parentOrganizationMapping.OrganizationName,
                        OrganizationName = a?.Account ?? parentOrganizationMapping.OrganizationName,
                        UserName = a?.Username ?? orgUser.Email,
                        CloudInfraAccountId = a?.AccountId,
                        OrganizationId = organizationId,
                        DomainName = a?.Domain,
                        DomainId = a?.DomainId.ToString(),
                        RoleName = a?.RoleName ?? "Regular User",
                        IsMappedToCloudInfraUser = a?.Email != null
                    }).ToList();

                var onlyCloudInfraUsers = (from ciUser in accountUsersMapped
                    where !orgUsers.Any(myacUser => myacUser.Email == ciUser.Email)
                    select new CloudInfraUserListModel
                    {
                        UserId = null,
                        CloudInfraUserId = ciUser.Id,
                        FirstName = ciUser.FirstName,
                        LastName = ciUser.LastName,
                        Email = ciUser.Email,
                        RoleName = ciUser.RoleName,
                        UserName = ciUser.Username,
                        OrganizationName = ciUser.Account,
                        OrganizationId = null,
                        CloudInfraAccountId = ciUser.AccountId,
                        DomainName = ciUser.Domain,
                        DomainId = ciUser.DomainId.ToString(),
                        AccountName = ciUser.Account,
                        IsLocalUser = true,
                        IsMappedToCloudInfraUser = true
                    }).ToList();

                userList.AddRange(mappedUsers);
                userList.AddRange(onlyCloudInfraUsers);
            }
            else
            {
                userList = orgUsers.Select(orgUser => new CloudInfraUserListModel
                {
                    UserId = orgUser.PersonId,
                    CloudInfraUserId = null,
                    FirstName = orgUser.FirstName,
                    LastName = orgUser.LastName,
                    Email = orgUser.Email,
                    RoleName = "Regular User",
                    UserName = orgUser.Email,
                    OrganizationName = parentOrganizationMapping?.OrganizationName ?? string.Empty,
                    OrganizationId = parentOrganizationMapping?.OrganizationId ?? organizationId,
                    CloudInfraAccountId = null,
                    DomainName = null,
                    DomainId = null,
                    AccountName = string.Empty,
                    IsMappedToCloudInfraUser = false
                }).ToList();
            }

            var usersToShow = userList.DistinctBy(e => e.Email).OrderBy(a => a.LastName).ToList();
            return usersToShow;
        }

        private async Task<AccountResponse> GetOrganizationAccount(ACOrganizationMappingModel organizationMapping)
        {
            if (organizationMapping != null && organizationMapping.AccountId != null)
            {
                return await _cloudStackApi.GetAccountById(organizationMapping.AccountId.Value);
            }

            return new AccountResponse();
        }

        public async Task<string> CreateCloudInfraAccount(int userId, int organizationId)
        {
            string accountName = string.Empty;
            var company = await _connectWiseService.GetCompanyByOrganization(organizationId);
            if (company == null)
            {
                var org = await _organizationService.GetActiveOrganizationById(organizationId, false);
                accountName = org.Name;
            }
            else
            {
                accountName = company.Name;
            }

            CreateAccountRequest accountRequest = new CreateAccountRequest();
            //kept this code to use existing MYAC user while create cloudInfra account
            if (userId > 0)
            {
                var member = await _dbContext.Person.AsNoTracking().FirstOrDefaultAsync(user => user.PersonId == userId);
                accountRequest.Username = member.Email;
                accountRequest.Firstname = member.FirstName;
                accountRequest.Lastname = member.LastName;
                accountRequest.Email = member.Email;
            }
            else
            {
                string userName = "temp-myac-" + Path.GetRandomFileName().Replace(".", "").Substring(0, 8);
                accountRequest.Email = userName + "@adaptivecloud.com";
                accountRequest.Firstname = "Test";
                accountRequest.Lastname = "Test_Myac";
                accountRequest.Username = userName;
            }

            accountRequest.Account = accountName;
            accountRequest.Accounttype = AccountType.User; // always default account type to User

            var parentOrganization = await _organizationService.GetRootOrganization(organizationId, true);
            var getMapping = await _adaptiveCloudService.GetMapping(parentOrganization.OrganizationId);
            if (parentOrganization.IsPartner && getMapping != null)
            {
                accountRequest.Domainid = getMapping.DomainId;
            }

            var account = await _cloudStackApi.CreateAccount(accountRequest);

            if (account == null)
            {
                // Error
                throw new BadRequestException("Failed to create Cloud Infrastructure Account.");
            }

            var newUser = account.user.FirstOrDefault();
            if (newUser == null)
            {
                throw new NotFoundException(userId.ToString(), "Cannot find new Cloud Infrastructure User.");
            }

            if (userId == 0)
            {
                var deleteUser = await _cloudStackApi.DeleteUser(newUser.Id.ToString());
                if (deleteUser.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return "Issue cannot delete new TempUser";
                }
            }

            // Add OrganizationMapping
            var ciMap = await _adaptiveCloudService.GetMapping(organizationId);
            if (ciMap == null)
            {
                ciMap = new Dto.ACOrganizationMappingModel
                {
                    AccountId = account.Id,
                    AccountName = accountName,
                };

                await _adaptiveCloudService.AddMapping(organizationId, ciMap);
            }
            else
            {
                ciMap.AccountId = account.Id;
                ciMap.AccountName = accountName;
                await _adaptiveCloudService.EditMapping(organizationId, ciMap.Id, ciMap);
            }

            return "Your Cloud Infrastructure trial is ready for use.";
        }

        public async Task<DomainAdminPanelUsersResponseDTO> GetDomainAdminPanelUsers(int organizationId)
        {
            DomainAdminPanelUsersResponseDTO response = new DomainAdminPanelUsersResponseDTO();

            List<CloudInfraUserListModel> accountUserList = new List<CloudInfraUserListModel>();
            List<CloudInfraUserListModel> orgUserList = new List<CloudInfraUserListModel>();
            var organization = await _organizationService.GetActiveOrganizationById(organizationId, false, true);
            var isRootOrg = organization.OrganizationId == Constants.RootOrganizationId;

            var parentOrganization = await _organizationService.GetRootOrganization(organizationId, true);
            var orgUsers = (await _personService.GetPersonsWithUserRolesInOrganizationByOrganizationId(parentOrganization.OrganizationId)).Where(a => !a.IsApiUser);

            var organizationAccount = await _adaptiveCloudService.GetMapping(parentOrganization.OrganizationId);

            var accountUsers = new List<User>();
            if (organizationAccount.DomainId != null)
            {
                accountUsers.AddRange(await _cloudStackApi.GetUsersByDomain(organizationAccount.DomainId.Value.ToString()));
            }
            else
            {
                var organizationMapping = await _adaptiveCloudService.GetMapping(organizationId);
                var accountCloudInfra = await _cloudStackApi.GetAccountById(organizationMapping.AccountId.Value);
                var users = accountCloudInfra != null ? accountCloudInfra.user : new List<Apis.CloudStack.Model.User>();
                accountUsers.AddRange(users);
            }

            var rootOrgMapping = await _adaptiveCloudService.GetMapping(Constants.RootOrganizationId);

            if (rootOrgMapping.DomainId.Value == organizationAccount.DomainId)
            {
                var rootAccount = await _cloudStackApi.GetAccountById(rootOrgMapping.AccountId.Value);
                var usersByAccount = rootAccount != null ? rootAccount.user : new List<Apis.CloudStack.Model.User>();
                accountUsers.AddRange(usersByAccount);
            }


            if (organizationAccount != null)
            {
                var adminAccountUsers = accountUsers.Where(a => a.RoleName != "User").ToList();

                accountUserList = (from adminUser in adminAccountUsers
                    join orgUser in orgUsers on adminUser.Email equals orgUser.Email
                        into temp
                    from a in temp.DefaultIfEmpty()
                    select new CloudInfraUserListModel
                    {
                        UserId = a?.PersonId,
                        CloudInfraUserId = adminUser.Id,
                        FirstName = adminUser.FirstName,
                        LastName = adminUser.LastName,
                        Email = adminUser.Email,
                        AccountName = adminUser.Account,
                        OrganizationName = adminUser.Account,
                        UserName = adminUser != null ? adminUser.Username : a.Email,
                        CloudInfraAccountId = adminUser.AccountId,
                        OrganizationId = organizationAccount.OrganizationId,
                        DomainName = organizationAccount.DomainName ?? null,
                        DomainId = organizationAccount?.DomainId?.ToString() ?? null,
                        RoleName = adminUser != null ? adminUser.RoleName : "User",
                        IsMappedToCloudInfraUser = (adminUser == null || adminUser.Email == null ? false : true)
                    }).ToList();
            }

            orgUserList = (from orgUser in orgUsers
                join user in accountUsers on orgUser.Email equals user.Email
                    into temp
                from a in temp.DefaultIfEmpty()
                select new CloudInfraUserListModel
                {
                    UserId = orgUser.PersonId,
                    FirstName = orgUser.FirstName,
                    LastName = orgUser.LastName,
                    CloudInfraUserId = a != null ? a.Id : null,
                    Email = orgUser.Email,
                    AccountName = a != null ? a.Account : null,
                    OrganizationName = a != null ? a.Account : parentOrganization.Name,
                    UserName = a != null ? a.Username : orgUser.Email,
                    CloudInfraAccountId = a != null ? a.AccountId : null,
                    OrganizationId = parentOrganization.OrganizationId,
                    DomainName = a != null ? a.Domain : null,
                    DomainId = a != null ? a.DomainId.ToString() : null,
                    RoleName = a != null ? a.RoleName : "User",
                    IsMappedToCloudInfraUser = (a == null || a.Email == null ? false : true)
                }).ToList();
            var adminRole = isRootOrg ? "Root Admin" : "Domain Admin";
            response.AdminUsers = accountUserList.Where(a => a.RoleName == adminRole).DistinctBy(a => a.Email).OrderBy(a => a.UserName).ToList();
            var adminUsersEmails = response.AdminUsers.Select(a => a.Email);
            var orgUsersNotInAccount = orgUserList.Where(a => !adminUsersEmails.Contains(a.Email)).DistinctBy(a => a.Email);


            response.OrgUsers.AddRange(orgUsersNotInAccount);

            if (!isRootOrg)
            {
                response.OrgUsers.AddRange(accountUserList.Where(a => !adminUsersEmails.Contains(a.Email)).DistinctBy(a => a.Email));
            }

            response.OrgUsers = response.OrgUsers.OrderBy(a => a.Email).ToList();
            response.OrganizationId = parentOrganization.OrganizationId;
            response.DomainAdminAccountId = organizationAccount != null ? organizationAccount.AccountId : null;
            response.DomainAdminDomainId = organizationAccount != null ? organizationAccount.DomainId : null;

            return response;
        }

        public CloudInfrastructureRoles GetRole(int organizationId)
        {
            var role = CloudInfrastructureRoles.Client;
            if (organizationId == Constants.RootOrganizationId)
            {
                role = CloudInfrastructureRoles.Root;
            }
            else
            {
                var isPartner = _dbContext.GetDescendantOrganizations(organizationId).FirstOrDefault(a => a.OrganizationId == organizationId).IsPartner || false;
                if (isPartner)
                {
                    role = CloudInfrastructureRoles.Partner;
                }
                else
                {
                    role = CloudInfrastructureRoles.Client;
                }
            }

            return role;
        }

        public async Task<bool> CanBeProvisioned(int organizationId)
        {
            var organization = await _organizationService.GetOrganizationById(organizationId);

            if (organization.IsPartner)
            {
                return true;
            }

            if (organization.ParentOrganizationId.HasValue
                && organization.ParentOrganizationId.Value == Constants.RootOrganizationId)
            {
                return true;
            }

            if (organization.ParentOrganizationId.HasValue)
            {
                //Organization has a parent Org
                var parentIsPartner = await _organizationService.IsPartnerOrganization(organization.ParentOrganizationId.Value);

                //Client Organizations Suborganizations cannot be provisioned. Requested in #MYAC-947
                if (parentIsPartner)
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<CloudInfraUserContext> GetCloudInfraUserContext(int organizationId, int userId)
        {
            var user = await _personRepository.GetPersonById(userId, true);

            var currentOrganizationMapping = await _organizationMappingRepository.GetCloudInfraMappingByOrganization(organizationId);
            var hasMappedDomain = currentOrganizationMapping.DomainId.HasValue;

            var cloudInfraUsers = await _cloudStackApi.GetUsersByUsername(user.Email);
            if (cloudInfraUsers.Count == 0)
            {
                _logger.LogWarning(
                    $"User {user.Email} in organization {organizationId} attempted to access Virtual Machines but no matching user was found in Cloud Infrastructure.");
                throw new BadRequestException("You do not have permission to access Virtual Machines. Please contact your administrator to request Cloud Infrastructure access.");
            }

            var userKeys = await SetUserKeys(cloudInfraUsers, currentOrganizationMapping);

            // Retrieve user details and role permissions
            var ciUser = await _cloudStackApi.GetUser(userKeys.ApiKey);

            // Filter for unique "allow" permissions and map to rules
            var rolePermissions = (await _cloudStackApi.GetRolePermissions(ciUser.RoleId))
                .Where(p => p.Permission == "allow")
                .Select(p => p.Rule)
                .Distinct()
                .ToList();

            var config = await _configurationService.GetCloudInfrastructureConfiguration();

            var cpuCustomOfferingMaxValue = await _cloudStackApi.GetGlobalConfigurationSettings("vm.serviceoffering.cpu.cores.max");
            var memoryCustomOfferingMaxValue = await _cloudStackApi.GetGlobalConfigurationSettings("vm.serviceoffering.ram.size.max");
            var diskSizeCustomOfferingMaxValue = await _cloudStackApi.GetGlobalConfigurationSettings("custom.diskoffering.size.max");
            var diskSizeCustomOfferingMinValue = await _cloudStackApi.GetGlobalConfigurationSettings("custom.diskoffering.size.min");

            return new CloudInfraUserContext
            {
                ApiKey = userKeys.ApiKey,
                AccountId = currentOrganizationMapping.AccountId,
                AccountName = currentOrganizationMapping.AccountName,
                ApiUrl = config.ApiUrl,
                ApiVersion = config.ApiVersion,
                CpuCustomOfferingMaxValue = cpuCustomOfferingMaxValue.Count > 0 ? int.Parse(cpuCustomOfferingMaxValue.First().Value) : 0,
                DiskSizeCustomOfferingMaxValue = diskSizeCustomOfferingMaxValue.Count > 0 ? int.Parse(diskSizeCustomOfferingMaxValue.First().Value) : 0,
                DiskSizeCustomOfferingMinValue = diskSizeCustomOfferingMinValue.Count > 0 ? int.Parse(diskSizeCustomOfferingMinValue.First().Value) : 0,
                DomainId = currentOrganizationMapping.DomainId,
                HasMappedDomain = hasMappedDomain,
                MemoryCustomOfferingMaxValue = memoryCustomOfferingMaxValue.Count > 0 ? int.Parse(memoryCustomOfferingMaxValue.First().Value) : 0,
                Permissions = rolePermissions,
                RoleName = ciUser.RoleName,
                RoleType = ciUser.RoleType,
                SecretKey = userKeys.SecretKey
            };
        }

        private async Task<UserKeys> SetUserKeys(List<User> cloudInfraUsers, CloudInfraOrganizationMapping currentOrganizationMapping)
        {
            UserKeys userKeys = null;

            // If domainId is null, get account from Cloud Infra, to ensure correct DomainId is set
            if (currentOrganizationMapping.DomainId == null)
            {
                var account = await _cloudStackApi.GetAccountById(currentOrganizationMapping.AccountId);
                if (account == null)
                {
                    // No suitable account mapping found
                    _logger.LogError(
                        $"Failed to find suitable account for organizationId: {currentOrganizationMapping.OrganizationId}, accountId: {currentOrganizationMapping.AccountId}");
                    throw new BadRequestException("Invalid Account");
                }

                currentOrganizationMapping.DomainId = account.DomainId;
            }

            // Check if a user is mapped in scoped account
            var accountMappedUser = cloudInfraUsers.FirstOrDefault(u => u.AccountId == currentOrganizationMapping.AccountId);
            if (accountMappedUser != null)
            {
                userKeys = await GetUserKeys(accountMappedUser.Id);
                return userKeys;
            }

            // Check if user exists in mapped domain and is domain admin
            var domainMappedUser = cloudInfraUsers.FirstOrDefault(u => u.DomainId == currentOrganizationMapping.DomainId && u.IsDomainAdmin);
            if (domainMappedUser != null)
            {
                userKeys = await GetUserKeys(domainMappedUser.Id);
                return userKeys;
            }

            // Check if user is root admin
            var rootAdminUser = cloudInfraUsers.FirstOrDefault(u => u.IsRootAdmin);
            if (rootAdminUser != null)
            {
                userKeys = await GetUserKeys(rootAdminUser.Id);
                return userKeys;
            }

            // If at this point userKeys is still null, then for some reason none of the conditions above is met, so we need to figure out what went wrong based on the logs below.
            if (userKeys == null)
            {
                var message = $"Failed to resolve credentials for User {cloudInfraUsers[0].Username}. " +
                              $"Organization {currentOrganizationMapping.OrganizationId}. " +
                              $"Domain {currentOrganizationMapping.DomainId}. " +
                              $"Account {currentOrganizationMapping.AccountId} ";
                var ex = new Exception(message);
                _logger.LogError(ex, message);
                throw ex;
            }

            return userKeys;
        }

        private async Task<UserKeys> GetUserKeys(Guid userId)
        {
            var userKeys = await _cloudStackApi.GetUserKeys(userId);

            // Register new keys if no keys are found
            if (userKeys?.ApiKey is null || userKeys.SecretKey is null)
            {
                userKeys = await _cloudStackApi.RegisterUserKeys(userId);
            }

            return userKeys;
        }
    }
}