For each bug fix, feature update, or ticket resolution, create a changelog file named `{branchName}` in the `/changes` folder (where `{branchName}` is the actual name of the repository being modified).

Each changelog file should document the changes using the following structure:

## Change Documentation Format

### Non-Technical Summary
- **Issue**: Brief description of the problem or feature request
- **Solution**: What was fixed or implemented in plain language
- **Key Changes**: Explain the most important code modifications in simple, business-friendly language that non-developers can understand. Focus on the primary functional improvements or fixes that deliver the most value.
- **User Impact**: How this change affects end users

### Technical Details
- **Primary Code Changes**: Highlight the most significant technical modifications that other developers should be aware of:
  - The core architectural or implementation changes
  - The main files or components that were substantially modified
  - The biggest impact changes that affect system behavior or performance
- **Files Modified**: List of specific files that were changed (prioritize the most critical ones)
- **Code Changes**: Description of the technical implementation
- **Dependencies**: Any new dependencies or version updates
- **Testing**: How the changes were validated

### Example Structure:
```markdown
# Repository Name - Changes

## [Date] - Issue Title

### Non-Technical Summary
**Issue**: The Clear button in VM creation form wasn't working properly
**Solution**: Fixed the Clear button to properly reset all form fields
**Key Changes**: Implemented automatic form synchronization that ensures when users click "Clear", the form immediately reflects the reset state. The most critical improvement was preserving the zone selection and template availability, which prevents users from losing their context and having to restart the entire process.
**User Impact**: Users can now successfully clear the form and start over without losing access to available options

### Technical Details
**Primary Code Changes**:
- Replaced observable-based form updates with Angular signal effects for real-time synchronization
- Redesigned the store reset logic to selectively preserve zone-dependent data while clearing user inputs
- Modified the setup component to automatically react to store state changes

**Files Modified**:
- setup.component.ts (form synchronization logic)
- create-vm-wizard-store.ts (reset method redesign)

**Code Changes**: Added effect-based subscriptions and modified reset method
**Dependencies**: None
**Testing**: Build completed successfully, no TypeScript errors
```

## Documentation Guidelines

### Prioritizing Changes
When documenting changes, focus on impact and significance rather than listing all modifications equally:

**For Key Changes (Non-Technical)**:
- Identify the 1-3 most important functional improvements
- Explain how these changes solve the core business problem
- Use language that stakeholders and product managers can understand
- Focus on user-facing benefits and workflow improvements

**For Primary Code Changes (Technical)**:
- Highlight architectural decisions that affect future development
- Identify changes that other developers must understand to work with the code
- Prioritize modifications that impact performance, security, or maintainability
- Focus on changes that establish new patterns or break from existing conventions

### Writing Effective Descriptions
- **Be Specific**: Instead of "Fixed form issues", write "Implemented real-time form synchronization to prevent data loss"
- **Show Impact**: Explain why the change matters, not just what was changed
- **Use Active Voice**: "Redesigned the reset logic" instead of "The reset logic was changed"
- **Provide Context**: Help readers understand the problem that necessitated the change

This approach ensures consistent documentation across all repositories and provides both technical and business context for each change, making the documentation valuable for decision-making and future development work.